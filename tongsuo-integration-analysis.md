# Tongsuo密码学库集成方案

## 1. 引言

本文档分析了trusty-tee项目中BoringSSL的集成方式，并基于此设计了Tongsuo密码学库的集成方案。目标是实现Tongsuo与BoringSSL的无缝替换，保持相同的使用体验，同时解决头文件冲突问题。

## 2. 现有BoringSSL集成分析

### 2.1 目录结构

```
trusty-tee/
├── opensource_libs/boringssl/          # BoringSSL源码目录
│   ├── src/include/                    # 头文件目录
│   ├── crypto-sources.mk              # 构建源文件列表
│   ├── sources.mk                      # 源文件定义
│   └── rules.mk                        # 构建规则
└── user/app/*/rules.mk                 # TA应用构建文件
    └── MODULE_LIBRARY_DEPS += external/boringssl
```

### 2.2 构建系统集成机制

#### 2.2.1 路径映射机制
- **直接路径引用**：构建系统直接使用`external/boringssl`作为库依赖
- **实际路径映射**：`external/boringssl` → `opensource_libs/boringssl`
- **无符号链接**：项目中不存在`external/boringssl`符号链接

#### 2.2.2 构建流程
1. **依赖解析**：`make/compability.mk`处理`MODULE_LIBRARY_DEPS`
2. **路径转换**：`LIB_SRC_DEPS`直接包含库路径
3. **递归包含**：通过`include-lib`宏递归包含依赖库的`rules.mk`
4. **库构建**：`make/rctee_lib.mk`生成静态库文件

#### 2.2.3 关键构建文件
- `opensource_libs/boringssl/rules.mk`：主构建规则
- `opensource_libs/boringssl/crypto-sources.mk`：源文件列表
- `opensource_libs/boringssl/sources.mk`：源文件定义

### 2.3 TA应用使用方式

```makefile
# user/app/cast-auth/app/rules.mk
MODULE_LIBRARY_DEPS += \
    trusty/user/base/lib/libstdc++-trusty \
    external/boringssl \                    # 直接引用
    trusty/user/base/lib/keybox/client \
```

### 2.4 头文件包含机制

```c
// TA应用代码中
#include <openssl/crypto.h>    // 通过MODULE_EXPORT_INCLUDES自动找到
#include <openssl/evp.h>
```

## 3. Tongsuo集成方案设计

### 3.1 整体架构

```
trusty-tee/
├── opensource_libs/Tongsuo/            # Tongsuo源码目录
│   ├── include/openssl/                # 头文件目录
│   ├── crypto-sources.mk              # 构建源文件列表
│   ├── sources.mk                      # 源文件定义
│   ├── rules.mk                        # 构建规则
│   └── trusty_config.h                 # Trusty特定配置
└── kernel/lk/external/                 # 符号链接目录
    └── Tongsuo -> ../../opensource_libs/Tongsuo
```

### 3.2 符号前缀重命名方案

#### 3.2.1 命名空间隔离
为避免Tongsuo和BoringSSL头文件冲突，采用符号前缀重命名：

```c
// 原始符号
EVP_CIPHER_CTX_new()
RSA_new()
BN_new()

// Tongsuo重命名后
TONGSUO_EVP_CIPHER_CTX_new()
TONGSUO_RSA_new()
TONGSUO_BN_new()
```

#### 3.2.2 头文件重命名
```c
// 原始头文件
#include <openssl/evp.h>
#include <openssl/rsa.h>

// Tongsuo头文件
#include <tongsuo/evp.h>
#include <tongsuo/rsa.h>
```

### 3.3 构建配置

#### 3.3.1 Tongsuo rules.mk
```makefile
# opensource_libs/Tongsuo/rules.mk
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

# Tongsuo特定配置
MODULE_CFLAGS += -DOPENSSL_NO_ASM -DOPENSSL_SMALL
MODULE_CFLAGS += -DTONGSUO_PREFIX_SYMBOLS
MODULE_CFLAGS += -include $(LOCAL_DIR)/trusty_config.h

# 符号前缀重命名
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

# 头文件导出
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

include $(LOCAL_DIR)/crypto-sources.mk
include make/rctee_lib.mk
```

#### 3.3.2 符号链接创建
```bash
# 自动化集成脚本
ln -sf ../../opensource_libs/Tongsuo kernel/lk/external/Tongsuo
```

### 3.4 使用方式

#### 3.4.1 TA应用构建配置
```makefile
# 选择密码学库（二选一）
ifeq ($(USE_TONGSUO),true)
MODULE_LIBRARY_DEPS += external/Tongsuo
else
MODULE_LIBRARY_DEPS += external/boringssl
endif
```

#### 3.4.2 代码适配
```c
// 条件编译适配
#ifdef USE_TONGSUO
#include <tongsuo/evp.h>
#define CRYPTO_EVP_CIPHER_CTX_new TONGSUO_EVP_CIPHER_CTX_new
#else
#include <openssl/evp.h>
#define CRYPTO_EVP_CIPHER_CTX_new EVP_CIPHER_CTX_new
#endif
```

## 4. 实施步骤

### 4.1 阶段一：基础集成
1. 复制Tongsuo源码到`opensource_libs/Tongsuo/`
2. 创建`trusty_config.h`配置文件
3. 编写`rules.mk`构建规则
4. 生成`crypto-sources.mk`和`sources.mk`

### 4.2 阶段二：符号重命名
1. 实现符号前缀重命名机制
2. 生成重命名后的头文件
3. 创建符号映射表
4. 测试符号冲突解决

### 4.3 阶段三：自动化集成
1. 创建符号链接自动化脚本
2. 集成到构建系统
3. 实现库选择机制
4. 完善文档和测试

## 5. 技术挑战与解决方案

### 5.1 头文件冲突
**问题**：Tongsuo和BoringSSL都使用`openssl/`头文件路径
**解决**：采用符号前缀重命名和头文件路径重命名

### 5.2 符号冲突
**问题**：两个库导出相同的符号名
**解决**：编译时符号前缀重命名，运行时隔离

### 5.3 构建兼容性
**问题**：保持与现有构建系统的兼容性
**解决**：遵循现有构建模式，最小化修改

## 6. 具体实现方案

### 6.1 符号前缀重命名实现

#### 6.1.1 预处理器宏定义
```c
// opensource_libs/Tongsuo/include/tongsuo/tongsuo_prefix.h
#ifndef TONGSUO_PREFIX_H
#define TONGSUO_PREFIX_H

// 核心加密函数重命名
#define EVP_CIPHER_CTX_new          TONGSUO_EVP_CIPHER_CTX_new
#define EVP_CIPHER_CTX_free         TONGSUO_EVP_CIPHER_CTX_free
#define EVP_EncryptInit_ex          TONGSUO_EVP_EncryptInit_ex
#define EVP_DecryptInit_ex          TONGSUO_EVP_DecryptInit_ex
#define RSA_new                     TONGSUO_RSA_new
#define RSA_free                    TONGSUO_RSA_free
#define BN_new                      TONGSUO_BN_new
#define BN_free                     TONGSUO_BN_free

// SM算法特有函数
#define SM2_compute_key             TONGSUO_SM2_compute_key
#define SM3_Init                    TONGSUO_SM3_Init
#define SM4_encrypt                 TONGSUO_SM4_encrypt

#endif // TONGSUO_PREFIX_H
```

#### 6.1.2 编译时符号重命名
```makefile
# opensource_libs/Tongsuo/rules.mk 中添加
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX
MODULE_CFLAGS += -include $(LOCAL_DIR)/include/tongsuo/tongsuo_prefix.h
```

### 6.2 中间适配层设计

#### 6.2.1 统一接口封装
```c
// user/base/lib/crypto-adapter/crypto_adapter.h
#ifndef CRYPTO_ADAPTER_H
#define CRYPTO_ADAPTER_H

#ifdef USE_TONGSUO
#include <tongsuo/evp.h>
#include <tongsuo/rsa.h>
#include <tongsuo/sm2.h>
#include <tongsuo/sm3.h>
#include <tongsuo/sm4.h>
#define CRYPTO_PREFIX TONGSUO_
#else
#include <openssl/evp.h>
#include <openssl/rsa.h>
#define CRYPTO_PREFIX
#endif

// 统一接口宏
#define CRYPTO_EVP_CIPHER_CTX_new()     CRYPTO_PREFIX##EVP_CIPHER_CTX_new()
#define CRYPTO_RSA_new()                CRYPTO_PREFIX##RSA_new()

// SM算法接口（仅Tongsuo支持）
#ifdef USE_TONGSUO
#define CRYPTO_SM2_compute_key          TONGSUO_SM2_compute_key
#define CRYPTO_SM3_Init                 TONGSUO_SM3_Init
#define CRYPTO_SM4_encrypt              TONGSUO_SM4_encrypt
#endif

#endif // CRYPTO_ADAPTER_H
```

### 6.3 构建系统增强

#### 6.3.1 库选择机制
```makefile
# make/crypto_lib_select.mk
CRYPTO_LIB_CHOICE ?= boringssl

ifeq ($(CRYPTO_LIB_CHOICE),tongsuo)
CRYPTO_LIB_DEP := external/Tongsuo
CRYPTO_DEFINES += -DUSE_TONGSUO=1
else ifeq ($(CRYPTO_LIB_CHOICE),boringssl)
CRYPTO_LIB_DEP := external/boringssl
CRYPTO_DEFINES += -DUSE_BORINGSSL=1
else
$(error Invalid CRYPTO_LIB_CHOICE: $(CRYPTO_LIB_CHOICE), must be 'tongsuo' or 'boringssl')
endif

# 导出给应用使用
GLOBAL_DEFINES += $(CRYPTO_DEFINES)
```

#### 6.3.2 应用构建模板
```makefile
# user/base/make/crypto_app.mk
# 为使用密码学库的应用提供的通用模板

include make/crypto_lib_select.mk

MODULE_LIBRARY_DEPS += \
    $(CRYPTO_LIB_DEP) \
    user/base/lib/crypto-adapter

MODULE_DEFINES += $(CRYPTO_DEFINES)
```

### 6.4 自动化集成脚本

#### 6.4.1 集成脚本
```bash
#!/bin/bash
# scripts/setup_tongsuo.sh

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Setting up Tongsuo integration..."

# 创建符号链接
EXTERNAL_DIR="$PROJECT_ROOT/kernel/lk/external"
TONGSUO_LINK="$EXTERNAL_DIR/Tongsuo"

if [ ! -d "$EXTERNAL_DIR" ]; then
    mkdir -p "$EXTERNAL_DIR"
fi

if [ -L "$TONGSUO_LINK" ]; then
    echo "Removing existing Tongsuo symlink..."
    rm "$TONGSUO_LINK"
fi

echo "Creating Tongsuo symlink..."
ln -sf "../../../opensource_libs/Tongsuo" "$TONGSUO_LINK"

# 验证集成
if [ -f "$TONGSUO_LINK/rules.mk" ]; then
    echo "✓ Tongsuo integration setup completed successfully"
else
    echo "✗ Tongsuo integration setup failed"
    exit 1
fi

echo "You can now use Tongsuo by setting CRYPTO_LIB_CHOICE=tongsuo"
```

#### 6.4.2 构建脚本集成
```bash
# local_build.sh 中添加
if [ "$CRYPTO_LIB" = "tongsuo" ]; then
    echo "Setting up Tongsuo integration..."
    ./scripts/setup_tongsuo.sh
    export CRYPTO_LIB_CHOICE=tongsuo
fi
```

## 7. 测试验证方案

### 7.1 单元测试
```c
// test/crypto_adapter_test.c
#include "crypto_adapter.h"
#include <assert.h>

void test_basic_crypto_operations() {
    // 测试基本加密操作
    EVP_CIPHER_CTX *ctx = CRYPTO_EVP_CIPHER_CTX_new();
    assert(ctx != NULL);

    // 测试RSA操作
    RSA *rsa = CRYPTO_RSA_new();
    assert(rsa != NULL);

    // 清理
    EVP_CIPHER_CTX_free(ctx);
    RSA_free(rsa);
}

#ifdef USE_TONGSUO
void test_sm_algorithms() {
    // 测试SM算法（仅Tongsuo支持）
    // SM2/SM3/SM4测试代码
}
#endif
```

### 7.2 集成测试
```makefile
# test/integration/rules.mk
MODULE := $(LOCAL_DIR)

include make/crypto_app.mk

MODULE_SRCS := \
    $(LOCAL_DIR)/crypto_integration_test.c

MANIFEST := $(LOCAL_DIR)/manifest.json

include make/trusted_app.mk
```

## 8. 预期效果

1. **无缝替换**：Tongsuo能够完全替换BoringSSL
2. **共存支持**：两个库可以在同一项目中共存
3. **自动化集成**：无需手动创建符号链接
4. **向后兼容**：现有TA应用无需修改即可使用
5. **SM算法支持**：提供国密算法能力
6. **性能优化**：针对Trusty环境优化
