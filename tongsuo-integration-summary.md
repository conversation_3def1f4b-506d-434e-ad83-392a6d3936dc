# Tongsuo密码学库集成方案总结

## 执行摘要

本文档提供了将Tongsuo密码学库集成到trusty-tee项目的完整技术方案。通过深入分析现有BoringSSL的集成机制，设计了一套无缝替换方案，实现了以下目标：

- **无缝替换**：Tongsuo可以完全替换BoringSSL，保持相同的使用体验
- **命名空间隔离**：通过符号前缀重命名解决头文件冲突问题
- **自动化集成**：无需手动创建符号链接，实现自动化构建
- **架构兼容**：遵循项目现有的最小修改原则，保持架构一致性

## 关键技术发现

### 1. BoringSSL集成机制分析

经过详细分析，发现trusty-tee项目中BoringSSL的集成采用以下机制：

```
TA应用 → MODULE_LIBRARY_DEPS += external/boringssl
       ↓
构建系统 → make/compability.mk 处理依赖
       ↓
路径解析 → 直接映射到 opensource_libs/boringssl
       ↓
模块构建 → include rules.mk → make/rctee_lib.mk
       ↓
静态库生成 → libboringssl.a
```

**关键发现**：
- 无需符号链接，构建系统直接使用路径映射
- 通过`rules.mk`实现模块化构建
- `make/compability.mk`是依赖解析的核心

### 2. 构建系统路径解析

分析`make/compability.mk`发现：
- `LIB_SRC_DEPS`直接包含库路径
- `include-lib`宏递归包含依赖库的`rules.mk`
- `notdir`函数提取库名用于生成静态库文件名

## 集成方案设计

### 1. 架构设计原则

- **最小修改**：最大程度复用现有构建基础设施
- **向后兼容**：现有TA应用无需修改即可使用
- **命名空间隔离**：通过符号前缀避免冲突
- **模块化设计**：支持库选择和切换

### 2. 核心技术方案

#### 2.1 符号前缀重命名
```c
// 原始符号 → Tongsuo符号
EVP_CIPHER_CTX_new    → TONGSUO_EVP_CIPHER_CTX_new
RSA_new               → TONGSUO_RSA_new
SM2_compute_key       → TONGSUO_SM2_compute_key
```

#### 2.2 头文件路径重命名
```c
// 原始路径 → Tongsuo路径
#include <openssl/evp.h>    → #include <tongsuo/evp.h>
#include <openssl/sm2.h>    → #include <tongsuo/sm2.h>
```

#### 2.3 统一适配层
```c
// 条件编译适配
#ifdef USE_TONGSUO
#include <tongsuo/evp.h>
#define CRYPTO_EVP_CIPHER_CTX_new TONGSUO_EVP_CIPHER_CTX_new
#else
#include <openssl/evp.h>
#define CRYPTO_EVP_CIPHER_CTX_new EVP_CIPHER_CTX_new
#endif
```

### 3. 目录结构

```
trusty-tee/
├── opensource_libs/Tongsuo/            # Tongsuo源码
│   ├── include/tongsuo/                # 重命名头文件
│   ├── rules.mk                        # 构建规则
│   ├── crypto-sources.mk               # 源文件列表
│   └── trusty_config.h                 # Trusty配置
├── user/base/lib/crypto-adapter/       # 适配层
│   ├── include/crypto_adapter.h        # 统一接口
│   └── rules.mk                        # 适配层构建
└── make/crypto_lib_select.mk           # 库选择机制
```

## 实施要点

### 1. 构建配置

#### 1.1 Tongsuo rules.mk关键配置
```makefile
# 符号前缀重命名
MODULE_CFLAGS += -DTONGSUO_PREFIX_SYMBOLS
MODULE_CFLAGS += -include $(LOCAL_DIR)/include/tongsuo/tongsuo_prefix.h

# Trusty优化配置
MODULE_CFLAGS += -DOPENSSL_NO_ASM -DOPENSSL_SMALL
MODULE_CFLAGS += -include $(LOCAL_DIR)/trusty_config.h

# 头文件导出
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include
```

#### 1.2 库选择机制
```makefile
# make/crypto_lib_select.mk
CRYPTO_LIB_CHOICE ?= boringssl

ifeq ($(CRYPTO_LIB_CHOICE),tongsuo)
CRYPTO_LIB_DEP := external/Tongsuo
CRYPTO_DEFINES += -DUSE_TONGSUO=1
else
CRYPTO_LIB_DEP := external/boringssl
CRYPTO_DEFINES += -DUSE_BORINGSSL=1
endif
```

### 2. 使用方式

#### 2.1 TA应用集成
```makefile
# user/app/example/rules.mk
CRYPTO_LIB_CHOICE := tongsuo
include make/crypto_app.mk

MODULE_LIBRARY_DEPS += $(CRYPTO_LIB_DEP)
```

#### 2.2 代码使用
```c
#include "crypto_adapter.h"

// 使用统一接口
EVP_CIPHER_CTX *ctx = CRYPTO_EVP_CIPHER_CTX_new();

#ifdef USE_TONGSUO
// 使用SM算法
SM3_CTX sm3_ctx;
CRYPTO_SM3_Init(&sm3_ctx);
#endif
```

## 技术优势

### 1. 无缝集成
- **零修改切换**：通过配置变量即可切换密码学库
- **接口兼容**：统一适配层确保接口一致性
- **构建兼容**：完全兼容现有构建系统

### 2. 命名空间隔离
- **符号冲突解决**：通过前缀重命名避免符号冲突
- **头文件隔离**：独立的头文件路径避免包含冲突
- **运行时隔离**：不同库的符号在运行时完全隔离

### 3. 扩展性
- **SM算法支持**：提供完整的国密算法能力
- **模块化设计**：支持未来添加更多密码学库
- **配置灵活**：支持细粒度的功能配置

## 实施建议

### 1. 分阶段实施
- **阶段一**：基础集成和编译验证
- **阶段二**：适配层开发和接口统一
- **阶段三**：全面测试和性能优化

### 2. 质量保证
- **单元测试**：确保基本功能正确性
- **集成测试**：验证与现有系统的兼容性
- **性能测试**：确保性能满足要求

### 3. 文档完善
- **技术文档**：详细的集成和使用文档
- **API文档**：统一接口的使用说明
- **迁移指南**：从BoringSSL迁移到Tongsuo的指南

## 结论

本方案通过深入分析trusty-tee项目的构建机制，设计了一套完整的Tongsuo集成方案。该方案具有以下特点：

1. **技术可行**：基于现有构建系统，技术风险可控
2. **实施简单**：遵循最小修改原则，实施复杂度低
3. **扩展性强**：支持未来的功能扩展和优化
4. **兼容性好**：与现有架构完全兼容

通过实施本方案，可以实现Tongsuo密码学库在trusty-tee项目中的无缝集成，为项目提供强大的国密算法支持能力。
