# Tongsuo集成实施方案

## 1. 实施概述

基于对trusty-tee项目BoringSSL集成方式的深入分析，本方案提供了Tongsuo密码学库的完整集成实施计划。方案遵循最小修改原则，确保与现有架构的兼容性。

## 2. 关键发现

### 2.1 BoringSSL集成机制
- **路径映射**：`external/boringssl` 直接映射到 `opensource_libs/boringssl`
- **无符号链接**：构建系统直接使用路径引用，无需符号链接
- **模块化构建**：通过`rules.mk`实现模块化构建
- **依赖解析**：`make/compability.mk`处理库依赖关系

### 2.2 构建流程分析
```
MODULE_LIBRARY_DEPS += external/boringssl
    ↓
make/compability.mk 处理依赖
    ↓
LIB_SRC_DEPS 包含库路径
    ↓
include-lib 宏递归包含 rules.mk
    ↓
make/rctee_lib.mk 生成静态库
```

## 3. Tongsuo集成架构

### 3.1 目录结构设计
```
trusty-tee/
├── opensource_libs/Tongsuo/            # Tongsuo源码
│   ├── include/openssl/                # 原始头文件
│   ├── include/tongsuo/                # 重命名头文件
│   ├── crypto/                         # 加密算法实现
│   ├── ssl/                           # SSL/TLS实现
│   ├── rules.mk                       # 构建规则
│   ├── crypto-sources.mk              # 源文件列表
│   ├── sources.mk                     # 源文件定义
│   └── trusty_config.h                # Trusty配置
├── user/base/lib/crypto-adapter/       # 适配层
│   ├── include/crypto_adapter.h        # 统一接口
│   └── rules.mk                       # 适配层构建
└── scripts/
    └── setup_tongsuo.sh              # 集成脚本
```

### 3.2 符号命名空间隔离

#### 3.2.1 符号前缀方案
```c
// 原始符号 → Tongsuo符号
EVP_CIPHER_CTX_new    → TONGSUO_EVP_CIPHER_CTX_new
RSA_new               → TONGSUO_RSA_new
BN_new                → TONGSUO_BN_new
SM2_compute_key       → TONGSUO_SM2_compute_key
SM3_Init              → TONGSUO_SM3_Init
SM4_encrypt           → TONGSUO_SM4_encrypt
```

#### 3.2.2 头文件路径重命名
```c
// 原始路径 → Tongsuo路径
#include <openssl/evp.h>    → #include <tongsuo/evp.h>
#include <openssl/rsa.h>    → #include <tongsuo/rsa.h>
#include <openssl/sm2.h>    → #include <tongsuo/sm2.h>
```

## 4. 核心实施文件

### 4.1 Tongsuo构建规则
```makefile
# opensource_libs/Tongsuo/rules.mk
LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# 重置本地变量
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# 包含源文件列表
MODULE_SRCDEPS += $(LOCAL_DIR)/crypto-sources.mk
include $(LOCAL_DIR)/crypto-sources.mk

# Tongsuo特定配置
MODULE_CFLAGS += -D__STDC_NO_ATOMICS__
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__

# Tongsuo优化配置
MODULE_CFLAGS += -DOPENSSL_NO_ASM -DOPENSSL_SMALL
MODULE_CFLAGS += -DOPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED
MODULE_CFLAGS += -DTONGSUO_PREFIX_SYMBOLS

# 包含Trusty配置
MODULE_CFLAGS += -include $(LOCAL_DIR)/trusty_config.h

# 符号前缀重命名
MODULE_CFLAGS += -include $(LOCAL_DIR)/include/tongsuo/tongsuo_prefix.h

# ARM优化配置
MODULE_STATIC_ARMCAP := -DOPENSSL_STATIC_ARMCAP
toarmcap = $(if $(filter-out 0 false,$(2)),-DOPENSSL_STATIC_ARMCAP_$(1),)
MODULE_STATIC_ARMCAP += $(call toarmcap,NEON,$(USE_ARM_V7_NEON))
MODULE_STATIC_ARMCAP += $(call toarmcap,AES,$(USE_ARM_V8_AES))
MODULE_STATIC_ARMCAP += $(call toarmcap,PMULL,$(USE_ARM_V8_PMULL))
MODULE_STATIC_ARMCAP += $(call toarmcap,SHA1,$(USE_ARM_V8_SHA1))
MODULE_STATIC_ARMCAP += $(call toarmcap,SHA256,$(USE_ARM_V8_SHA2))
MODULE_CFLAGS += $(MODULE_STATIC_ARMCAP)
MODULE_ASMFLAGS += $(MODULE_STATIC_ARMCAP)

# 浮点指令过滤
ifeq (false,$(call TOBOOL,$(ALLOW_FP_USE)))
LOCAL_SRC_FILES_$(ARCH) := $(filter-out linux-aarch64/crypto/chacha/chacha-armv8.S,$(LOCAL_SRC_FILES_$(ARCH)))
LOCAL_SRC_FILES_$(ARCH) := $(filter-out linux-aarch64/crypto/modes/ghash-neon-armv8.S,$(LOCAL_SRC_FILES_$(ARCH)))
endif

# 添加源文件
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES_$(ARCH)))

# 内部包含路径
MODULE_INCLUDES += $(LOCAL_DIR)/crypto

# 导出头文件路径
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# 包含OpenSSL存根
include kernel/hardware/nxp/user/lib/nxp_openssl_stub/rules.mk
include user/base/lib/openssl-stubs/openssl-stubs-inc.mk

include make/rctee_lib.mk
```

### 4.2 符号前缀定义
```c
// opensource_libs/Tongsuo/include/tongsuo/tongsuo_prefix.h
#ifndef TONGSUO_PREFIX_H
#define TONGSUO_PREFIX_H

#ifdef TONGSUO_PREFIX_SYMBOLS

// EVP接口重命名
#define EVP_CIPHER_CTX_new              TONGSUO_EVP_CIPHER_CTX_new
#define EVP_CIPHER_CTX_free             TONGSUO_EVP_CIPHER_CTX_free
#define EVP_CIPHER_CTX_init             TONGSUO_EVP_CIPHER_CTX_init
#define EVP_CIPHER_CTX_cleanup          TONGSUO_EVP_CIPHER_CTX_cleanup
#define EVP_EncryptInit_ex              TONGSUO_EVP_EncryptInit_ex
#define EVP_DecryptInit_ex              TONGSUO_EVP_DecryptInit_ex
#define EVP_EncryptUpdate               TONGSUO_EVP_EncryptUpdate
#define EVP_DecryptUpdate               TONGSUO_EVP_DecryptUpdate
#define EVP_EncryptFinal_ex             TONGSUO_EVP_EncryptFinal_ex
#define EVP_DecryptFinal_ex             TONGSUO_EVP_DecryptFinal_ex

// RSA接口重命名
#define RSA_new                         TONGSUO_RSA_new
#define RSA_free                        TONGSUO_RSA_free
#define RSA_generate_key_ex             TONGSUO_RSA_generate_key_ex
#define RSA_public_encrypt              TONGSUO_RSA_public_encrypt
#define RSA_private_decrypt             TONGSUO_RSA_private_decrypt

// BN接口重命名
#define BN_new                          TONGSUO_BN_new
#define BN_free                         TONGSUO_BN_free
#define BN_clear_free                   TONGSUO_BN_clear_free
#define BN_dup                          TONGSUO_BN_dup

// SM算法接口重命名
#define SM2_compute_key                 TONGSUO_SM2_compute_key
#define SM2_encrypt                     TONGSUO_SM2_encrypt
#define SM2_decrypt                     TONGSUO_SM2_decrypt
#define SM2_sign                        TONGSUO_SM2_sign
#define SM2_verify                      TONGSUO_SM2_verify

#define SM3_Init                        TONGSUO_SM3_Init
#define SM3_Update                      TONGSUO_SM3_Update
#define SM3_Final                       TONGSUO_SM3_Final

#define SM4_set_key                     TONGSUO_SM4_set_key
#define SM4_encrypt                     TONGSUO_SM4_encrypt
#define SM4_decrypt                     TONGSUO_SM4_decrypt

// 错误处理接口
#define ERR_get_error                   TONGSUO_ERR_get_error
#define ERR_error_string                TONGSUO_ERR_error_string
#define ERR_clear_error                 TONGSUO_ERR_clear_error

// 内存管理接口
#define OPENSSL_malloc                  TONGSUO_OPENSSL_malloc
#define OPENSSL_free                    TONGSUO_OPENSSL_free
#define OPENSSL_realloc                 TONGSUO_OPENSSL_realloc

#endif // TONGSUO_PREFIX_SYMBOLS

#endif // TONGSUO_PREFIX_H
```

### 4.3 Trusty配置文件
```c
// opensource_libs/Tongsuo/trusty_config.h
#ifndef TONGSUO_TRUSTY_CONFIG_H
#define TONGSUO_TRUSTY_CONFIG_H

// Trusty环境特定配置
#define OPENSSL_NO_SOCK
#define OPENSSL_NO_DGRAM
#define OPENSSL_NO_STDIO
#define OPENSSL_NO_POSIX_IO
#define OPENSSL_NO_FP_API

// 禁用不需要的功能
#define OPENSSL_NO_ENGINE
#define OPENSSL_NO_DYNAMIC_ENGINE
#define OPENSSL_NO_DEPRECATED
#define OPENSSL_NO_LEGACY

// 启用SM算法
#define OPENSSL_SM2
#define OPENSSL_SM3
#define OPENSSL_SM4

// 内存优化
#define OPENSSL_SMALL_FOOTPRINT

// 线程安全配置
#define OPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED

#endif // TONGSUO_TRUSTY_CONFIG_H
```

## 5. 使用方式

### 5.1 TA应用集成
```makefile
# user/app/example/rules.mk
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

# 选择密码学库
CRYPTO_LIB_CHOICE := tongsuo

include make/crypto_app.mk

MODULE_SRCS := \
    $(LOCAL_DIR)/main.c

MANIFEST := $(LOCAL_DIR)/manifest.json

include make/trusted_app.mk
```

### 5.2 代码使用示例
```c
// user/app/example/main.c
#include "crypto_adapter.h"

int main(void) {
    // 使用统一接口
    EVP_CIPHER_CTX *ctx = CRYPTO_EVP_CIPHER_CTX_new();
    
    #ifdef USE_TONGSUO
    // 使用SM算法（仅Tongsuo支持）
    SM3_CTX sm3_ctx;
    CRYPTO_SM3_Init(&sm3_ctx);
    #endif
    
    return 0;
}
```

## 6. 实施时间表

### 阶段一（1-2周）：基础集成
- [ ] 复制Tongsuo源码到指定目录
- [ ] 创建基础构建文件
- [ ] 实现符号前缀重命名
- [ ] 基础编译测试

### 阶段二（1周）：适配层开发
- [ ] 开发统一接口适配层
- [ ] 实现库选择机制
- [ ] 创建测试用例

### 阶段三（1周）：集成测试
- [ ] 完整构建测试
- [ ] 功能验证测试
- [ ] 性能对比测试
- [ ] 文档完善

## 7. 验收标准

1. **编译成功**：Tongsuo库能够成功编译
2. **功能正确**：基本加密功能正常工作
3. **SM算法**：国密算法功能验证通过
4. **兼容性**：与现有TA应用兼容
5. **性能**：性能满足要求
6. **文档**：完整的使用文档
