/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef TONGSUO_TRUSTY_CONFIG_H
#define TONGSUO_TRUSTY_CONFIG_H

/* Tongsuo configuration for Trusty */

/* Disable features not needed in Trusty */
#define OPENSSL_NO_APPS
#define OPENSSL_NO_ASYNC
#define OPENSSL_NO_AUTOERRINIT
#define OPENSSL_NO_AUTOLOAD_CONFIG
#define OPENSSL_NO_BF
#define OPENSSL_NO_CAMELLIA
#define OPENSSL_NO_CAST
#define OPENSSL_NO_CMS
#define OPENSSL_NO_COMP
#define OPENSSL_NO_CT
#define OPENSSL_NO_DEPRECATED
#define OPENSSL_NO_DGRAM
#define OPENSSL_NO_DSO
#define OPENSSL_NO_DTLS
#define OPENSSL_NO_DTLS1
#define OPENSSL_NO_DTLS1_2
#define OPENSSL_NO_DYNAMIC_ENGINE
#define OPENSSL_NO_EC_NISTP_64_GCC_128
#define OPENSSL_NO_EGD
#define OPENSSL_NO_ENGINE
#define OPENSSL_NO_ERR
#define OPENSSL_NO_FILENAMES
#define OPENSSL_NO_GOST
#define OPENSSL_NO_HEARTBEATS
#define OPENSSL_NO_HW
#define OPENSSL_NO_IDEA
#define OPENSSL_NO_KRB5
#define OPENSSL_NO_LOCKING
#define OPENSSL_NO_MD2
#define OPENSSL_NO_MDC2
#define OPENSSL_NO_NEXTPROTONEG
#define OPENSSL_NO_OCSP
#define OPENSSL_NO_POSIX_IO
#define OPENSSL_NO_PSK
#define OPENSSL_NO_RC2
#define OPENSSL_NO_RC5
#define OPENSSL_NO_RFC3779
#define OPENSSL_NO_SCRYPT
#define OPENSSL_NO_SEED
#define OPENSSL_NO_SOCK
#define OPENSSL_NO_SRP
#define OPENSSL_NO_SRTP
#define OPENSSL_NO_SSL_TRACE
#define OPENSSL_NO_SSL2
#define OPENSSL_NO_SSL3
#define OPENSSL_NO_STDIO
#define OPENSSL_NO_THREADS
#define OPENSSL_NO_TS
#define OPENSSL_NO_UI
#define OPENSSL_NO_UNIT_TEST
#define OPENSSL_NO_WHIRLPOOL

/* Enable features needed in Trusty */
#define OPENSSL_SMALL
#define OPENSSL_NO_ASM

/* Platform specific defines */
#define __TRUSTY__
#define __linux__

#endif /* TONGSUO_TRUSTY_CONFIG_H */
